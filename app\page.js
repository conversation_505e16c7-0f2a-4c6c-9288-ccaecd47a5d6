import Image from "next/image";

export default function Home() {
  return (
    <div className="px-4">
      <div className="relative w-full h-full flex justify-center items-center">
        <div className="hidden min-[950px]:block">
          <div className="absolute z-30 top-0 left-0 w-full h-[200px] pointer-events-none" style="mask-image: linear-gradient(rgb(255, 255, 255) 0%, rgb(255, 255, 255) 60%, rgba(255, 255, 255, 0) 100%); mask-size: 100% 100%; mask-repeat: no-repeat; background: rgb(255, 255, 255);">

          </div>
          <div className="absolute z-30 bottom-0 left-0 w-full h-[200px] pointer-events-none" style="mask-image: linear-gradient(0deg, rgb(255, 255, 255) 0%, rgb(255, 255, 255) 60%, rgba(255, 255, 255, 0) 100%); mask-size: 100% 100%; mask-repeat: no-repeat; background: rgb(255, 255, 255);">


          </div>

        </div>


        <div className="overflow-x-hidden w-full h-full min-[950px]:h-[770px] relative bg-white flex flex-col gap-10 lg:gap-0 lg:flex-row justify-center items-center max-w-[1600px] select-none">

          <div className="absolute top-1/2 left-[2%] min-[1170px]:left-auto min-[1170px]:-right-[30%] min-[1330px]:-right-[20%] min-[1570px]:-right-[10%] -translate-y-1/2 min-[950px]:block hidden">
            <div className="max-[1169px]:block" style="clip-path: polygon(0px 0px, 50% 0px, 50% 100%, 0% 100%);">
              <div className="flex justify-center z-50 items-center w-[754px] h-[754px] bg-[linear-gradient(90deg,rgba(226,230,245,0.8)_0%,rgba(255,255,255,0)_100%)] rounded-full">
                <div className="relative w-[720px] h-[720px] overflow-hidden">
                  <div className="absolute inset-0">
                    <div className="relative w-full h-full cursor-grab active:cursor-grabbing" style="transform: rotate(-954deg); transition: transform 0.3s ease-out;">
                      <div className="absolute inset-0 bg-white rounded-full shadow-inner border border-gray-100">
                      </div>
                      <div className="absolute bg-gray-400 left-1/2 top-1/2 transition-all" style="width: 2px; height: 20px; transform: translate(-50%, -50%) translate(1.91672e-14px, -313.024px) rotate(0deg);">
                      </div>
                      <div className="absolute bg-gray-400 left-1/2 top-1/2 transition-all" style="width: 1px; height: 10px; transform: translate(-50%, -50%) translate(10.3496px, -312.853px) rotate(1.89474deg);">
                      </div>
                      <div className="absolute bg-gray-400 left-1/2 top-1/2 transition-all" style="width: 1px; height: 10px; transform: translate(-50%, -50%) translate(20.6879px, -312.34px) rotate(3.78947deg);">
                      </div>
                      <div className="absolute bg-gray-400 left-1/2 top-1/2 transition-all" style="width: 1px; height: 10px; transform: translate(-50%, -50%) translate(31.0036px, -311.485px) rotate(5.68421deg);">
                      </div>
                      <div className="absolute bg-gray-400 left-1/2 top-1/2 transition-all" style="width: 1px; height: 10px; transform: translate(-50%, -50%) translate(41.2854px, -310.289px) rotate(7.57895deg);">
                      </div>
                      <div className="absolute bg-gray-400 left-1/2 top-1/2 transition-all" style="width: 1px; height: 10px; transform: translate(-50%, -50%) translate(51.5221px, -308.755px) rotate(9.47368deg);">
                      </div>
                      <div className="absolute bg-gray-400 left-1/2 top-1/2 transition-all" style="width: 1px; height: 10px; transform: translate(-50%, -50%) translate(61.7024px, -306.882px) rotate(11.3684deg);">
                      </div>
                      <div className="absolute bg-gray-400 left-1/2 top-1/2 transition-all" style="width: 1px; height: 10px; transform: translate(-50%, -50%) translate(71.8152px, -304.675px) rotate(13.2632deg);">
                      </div>
                      <div className="absolute bg-gray-400 left-1/2 top-1/2 transition-all" style="width: 1px; height: 10px; transform: translate(-50%, -50%) translate(81.8495px, -302.134px) rotate(15.1579deg);">
                      </div>
                      <div className="absolute bg-gray-400 left-1/2 top-1/2 transition-all" style="width: 1px; height: 10px; transform: translate(-50%, -50%) translate(91.7943px, -299.262px) rotate(17.0526deg);">
                      </div>
                      <div className="absolute bg-gray-400 left-1/2 top-1/2 transition-all" style="width: 1px; height: 10px; transform: translate(-50%, -50%) translate(101.639px, -296.063px) rotate(18.9474deg);">
                      </div>
                      <div className="absolute bg-gray-400 left-1/2 top-1/2 transition-all" style="width: 1px; height: 10px; transform: translate(-50%, -50%) translate(111.372px, -292.541px) rotate(20.8421deg);">
                      </div>
                      <div className="absolute bg-gray-400 left-1/2 top-1/2 transition-all" style="width: 1px; height: 10px; transform: translate(-50%, -50%) translate(120.984px, -288.699px) rotate(22.7368deg);">
                      </div>
                      <div className="absolute bg-gray-400 left-1/2 top-1/2 transition-all" style="width: 1px; height: 10px; transform: translate(-50%, -50%) translate(130.463px, -284.541px) rotate(24.6316deg);">
                      </div>
                      <div className="absolute bg-gray-400 left-1/2 top-1/2 transition-all" style="width: 1px; height: 10px; transform: translate(-50%, -50%) translate(139.799px, -280.072px) rotate(26.5263deg);">
                      </div>
                      <div className="absolute bg-gray-400 left-1/2 top-1/2 transition-all" style="width: 1px; height: 10px; transform: translate(-50%, -50%) translate(148.983px, -275.296px) rotate(28.4211deg);">
                      </div>
                      <div className="absolute bg-gray-400 left-1/2 top-1/2 transition-all" style="width: 1px; height: 10px; transform: translate(-50%, -50%) translate(158.004px, -270.22px) rotate(30.3158deg);">
                      </div>
                      <div className="absolute bg-gray-400 left-1/2 top-1/2 transition-all" style="width: 1px; height: 10px; transform: translate(-50%, -50%) translate(166.852px, -264.848px) rotate(32.2105deg);">
                      </div>
                      <div className="absolute bg-gray-400 left-1/2 top-1/2 transition-all" style="width: 1px; height: 10px; transform: translate(-50%, -50%) translate(175.517px, -259.187px) rotate(34.1053deg);">
                      </div>
                      <div className="absolute bg-gray-400 left-1/2 top-1/2 transition-all" style="width: 1px; height: 10px; transform: translate(-50%, -50%) translate(183.991px, -253.242px) rotate(36deg);">
                      </div>
                      <div className="absolute bg-gray-400 left-1/2 top-1/2 transition-all" style="width: 1px; height: 10px; transform: translate(-50%, -50%) translate(192.263px, -247.02px) rotate(37.8947deg);">
                      </div>
                      <div className="absolute bg-gray-400 left-1/2 top-1/2 transition-all" style="width: 1px; height: 10px; transform: translate(-50%, -50%) translate(200.326px, -240.528px) rotate(39.7895deg);">
                      </div>
                      <div className="absolute bg-gray-400 left-1/2 top-1/2 transition-all" style="width: 1px; height: 10px; transform: translate(-50%, -50%) translate(208.169px, -233.773px) rotate(41.6842deg);">
                      </div>
                      <div className="absolute bg-gray-400 left-1/2 top-1/2 transition-all" style="width: 1px; height: 10px; transform: translate(-50%, -50%) translate(215.784px, -226.762px) rotate(43.5789deg);">
                      </div>
                      <div className="absolute bg-gray-400 left-1/2 top-1/2 transition-all" style="width: 1px; height: 10px; transform: translate(-50%, -50%) translate(223.164px, -219.504px) rotate(45.4737deg);">
                      </div>
                      <div className="absolute bg-gray-400 left-1/2 top-1/2 transition-all" style="width: 1px; height: 10px; transform: translate(-50%, -50%) translate(230.299px, -212.005px) rotate(47.3684deg);">
                      </div>
                      <div className="absolute bg-gray-400 left-1/2 top-1/2 transition-all" style="width: 1px; height: 10px; transform: translate(-50%, -50%) translate(237.183px, -204.275px) rotate(49.2632deg);">
                      </div>
                      <div className="absolute bg-gray-400 left-1/2 top-1/2 transition-all" style="width: 1px; height: 10px; transform: translate(-50%, -50%) translate(243.807px, -196.321px) rotate(51.1579deg);">
                      </div>
                      <div className="absolute bg-gray-400 left-1/2 top-1/2 transition-all" style="width: 1px; height: 10px; transform: translate(-50%, -50%) translate(250.165px, -188.153px) rotate(53.0526deg);">
                      </div>
                      <div className="absolute bg-gray-400 left-1/2 top-1/2 transition-all" style="width: 1px; height: 10px; transform: translate(-50%, -50%) translate(256.249px, -179.779px) rotate(54.9474deg);">
                      </div>
                      <div className="absolute bg-gray-400 left-1/2 top-1/2 transition-all" style="width: 1px; height: 10px; transform: translate(-50%, -50%) translate(262.053px, -171.208px) rotate(56.8421deg);">
                      </div>
                      <div className="absolute bg-gray-400 left-1/2 top-1/2 transition-all" style="width: 1px; height: 10px; transform: translate(-50%, -50%) translate(267.571px, -162.45px) rotate(58.7368deg);">
                      </div>
                      <div className="absolute bg-gray-400 left-1/2 top-1/2 transition-all" style="width: 1px; height: 10px; transform: translate(-50%, -50%) translate(272.795px, -153.514px) rotate(60.6316deg);">
                      </div>
                      <div className="absolute bg-gray-400 left-1/2 top-1/2 transition-all" style="width: 1px; height: 10px; transform: translate(-50%, -50%) translate(277.722px, -144.411px) rotate(62.5263deg);">
                      </div>
                      <div className="absolute bg-gray-400 left-1/2 top-1/2 transition-all" style="width: 1px; height: 10px; transform: translate(-50%, -50%) translate(282.345px, -135.149px) rotate(64.4211deg);">
                      </div>
                      <div className="absolute bg-gray-400 left-1/2 top-1/2 transition-all" style="width: 1px; height: 10px; transform: translate(-50%, -50%) translate(286.659px, -125.74px) rotate(66.3158deg);">
                      </div>
                      <div className="absolute bg-gray-400 left-1/2 top-1/2 transition-all" style="width: 1px; height: 10px; transform: translate(-50%, -50%) translate(290.66px, -116.194px) rotate(68.2105deg);">
                      </div>
                      <div className="absolute bg-gray-400 left-1/2 top-1/2 transition-all" style="width: 1px; height: 10px; transform: translate(-50%, -50%) translate(294.343px, -106.52px) rotate(70.1053deg);">
                      </div>
                      <div className="absolute bg-gray-400 left-1/2 top-1/2 transition-all" style="width: 2px; height: 20px; transform: translate(-50%, -50%) translate(297.704px, -96.7297px) rotate(72deg);">
                      </div>
                      <div className="absolute bg-gray-400 left-1/2 top-1/2 transition-all" style="width: 1px; height: 10px; transform: translate(-50%, -50%) translate(300.739px, -86.8338px) rotate(73.8947deg);">
                      </div>
                      <div className="absolute bg-gray-400 left-1/2 top-1/2 transition-all" style="width: 1px; height: 10px; transform: translate(-50%, -50%) translate(303.446px, -76.8428px) rotate(75.7895deg);">
                      </div>
                      <div className="absolute bg-gray-400 left-1/2 top-1/2 transition-all" style="width: 1px; height: 10px; transform: translate(-50%, -50%) translate(305.82px, -66.7679px) rotate(77.6842deg);">
                      </div>
                      <div className="absolute bg-gray-400 left-1/2 top-1/2 transition-all" style="width: 1px; height: 10px; transform: translate(-50%, -50%) translate(307.861px, -56.6199px) rotate(79.5789deg);">
                      </div>
                      <div className="absolute bg-gray-400 left-1/2 top-1/2 transition-all" style="width: 1px; height: 10px; transform: translate(-50%, -50%) translate(309.564px, -46.4101px) rotate(81.4737deg);">
                      </div>
                      <div className="absolute bg-gray-400 left-1/2 top-1/2 transition-all" style="width: 1px; height: 10px; transform: translate(-50%, -50%) translate(310.93px, -36.1495px) rotate(83.3684deg);">
                      </div>
                      <div className="absolute bg-gray-400 left-1/2 top-1/2 transition-all" style="width: 1px; height: 10px; transform: translate(-50%, -50%) translate(311.955px, -25.8493px) rotate(85.2632deg);">
                      </div>
                      <div className="absolute bg-gray-400 left-1/2 top-1/2 transition-all" style="width: 1px; height: 10px; transform: translate(-50%, -50%) translate(312.639px, -15.5209px) rotate(87.1579deg);">
                      </div>
                      <div className="absolute bg-gray-400 left-1/2 top-1/2 transition-all" style="width: 1px; height: 10px; transform: translate(-50%, -50%) translate(312.981px, -5.17552px) rotate(89.0526deg);">
                      </div>
                      <div className="absolute bg-gray-400 left-1/2 top-1/2 transition-all" style="width: 1px; height: 10px; transform: translate(-50%, -50%) translate(312.981px, 5.17552px) rotate(90.9474deg);">
                      </div>
                      <div className="absolute bg-gray-400 left-1/2 top-1/2 transition-all" style="width: 1px; height: 10px; transform: translate(-50%, -50%) translate(312.639px, 15.5209px) rotate(92.8421deg);">
                      </div>
                      <div className="absolute bg-gray-400 left-1/2 top-1/2 transition-all" style="width: 1px; height: 10px; transform: translate(-50%, -50%) translate(311.955px, 25.8493px) rotate(94.7368deg);">
                      </div>
                      <div className="absolute bg-gray-400 left-1/2 top-1/2 transition-all" style="width: 1px; height: 10px; transform: translate(-50%, -50%) translate(310.93px, 36.1495px) rotate(96.6316deg);">
                      </div>
                      <div className="absolute bg-gray-400 left-1/2 top-1/2 transition-all" style="width: 1px; height: 10px; transform: translate(-50%, -50%) translate(309.564px, 46.4101px) rotate(98.5263deg);">
                      </div>
                      <div className="absolute bg-gray-400 left-1/2 top-1/2 transition-all" style="width: 1px; height: 10px; transform: translate(-50%, -50%) translate(307.861px, 56.6199px) rotate(100.421deg);">
                      </div>
                      <div className="absolute bg-gray-400 left-1/2 top-1/2 transition-all" style="width: 1px; height: 10px; transform: translate(-50%, -50%) translate(305.82px, 66.7679px) rotate(102.316deg);">
                      </div>
                      <div className="absolute bg-gray-400 left-1/2 top-1/2 transition-all" style="width: 1px; height: 10px; transform: translate(-50%, -50%) translate(303.446px, 76.8428px) rotate(104.211deg);">
                      </div>
                      <div className="absolute bg-gray-400 left-1/2 top-1/2 transition-all" style="width: 1px; height: 10px; transform: translate(-50%, -50%) translate(300.739px, 86.8338px) rotate(106.105deg);">
                      </div>
                      <div className="absolute bg-gray-400 left-1/2 top-1/2 transition-all" style="width: 1px; height: 10px; transform: translate(-50%, -50%) translate(297.704px, 96.7297px) rotate(108deg);">
                      </div>
                      <div className="absolute bg-gray-400 left-1/2 top-1/2 transition-all" style="width: 1px; height: 10px; transform: translate(-50%, -50%) translate(294.343px, 106.52px) rotate(109.895deg);">
                      </div>
                      <div className="absolute bg-gray-400 left-1/2 top-1/2 transition-all" style="width: 1px; height: 10px; transform: translate(-50%, -50%) translate(290.66px, 116.194px) rotate(111.789deg);">
                      </div>
                      <div className="absolute bg-gray-400 left-1/2 top-1/2 transition-all" style="width: 1px; height: 10px; transform: translate(-50%, -50%) translate(286.659px, 125.74px) rotate(113.684deg);">
                      </div>
                      <div className="absolute bg-gray-400 left-1/2 top-1/2 transition-all" style="width: 1px; height: 10px; transform: translate(-50%, -50%) translate(282.345px, 135.149px) rotate(115.579deg);">
                      </div>
                      <div className="absolute bg-gray-400 left-1/2 top-1/2 transition-all" style="width: 1px; height: 10px; transform: translate(-50%, -50%) translate(277.722px, 144.411px) rotate(117.474deg);">
                      </div>
                      <div className="absolute bg-gray-400 left-1/2 top-1/2 transition-all" style="width: 1px; height: 10px; transform: translate(-50%, -50%) translate(272.795px, 153.514px) rotate(119.368deg);">
                      </div>
                      <div className="absolute bg-gray-400 left-1/2 top-1/2 transition-all" style="width: 1px; height: 10px; transform: translate(-50%, -50%) translate(267.571px, 162.45px) rotate(121.263deg);">
                      </div>
                      <div className="absolute bg-gray-400 left-1/2 top-1/2 transition-all" style="width: 1px; height: 10px; transform: translate(-50%, -50%) translate(262.053px, 171.208px) rotate(123.158deg);">
                      </div>
                      <div className="absolute bg-gray-400 left-1/2 top-1/2 transition-all" style="width: 1px; height: 10px; transform: translate(-50%, -50%) translate(256.249px, 179.779px) rotate(125.053deg);">
                      </div>
                      <div className="absolute bg-gray-400 left-1/2 top-1/2 transition-all" style="width: 1px; height: 10px; transform: translate(-50%, -50%) translate(250.165px, 188.153px) rotate(126.947deg);">
                      </div>
                      <div className="absolute bg-gray-400 left-1/2 top-1/2 transition-all" style="width: 1px; height: 10px; transform: translate(-50%, -50%) translate(243.807px, 196.321px) rotate(128.842deg);">
                      </div>
                      <div className="absolute bg-gray-400 left-1/2 top-1/2 transition-all" style="width: 1px; height: 10px; transform: translate(-50%, -50%) translate(237.183px, 204.275px) rotate(130.737deg);">
                      </div>
                      <div className="absolute bg-gray-400 left-1/2 top-1/2 transition-all" style="width: 1px; height: 10px; transform: translate(-50%, -50%) translate(230.299px, 212.005px) rotate(132.632deg);">
                      </div>
                      <div className="absolute bg-gray-400 left-1/2 top-1/2 transition-all" style="width: 1px; height: 10px; transform: translate(-50%, -50%) translate(223.164px, 219.504px) rotate(134.526deg);">
                      </div>
                      <div className="absolute bg-gray-400 left-1/2 top-1/2 transition-all" style="width: 1px; height: 10px; transform: translate(-50%, -50%) translate(215.784px, 226.762px) rotate(136.421deg);">
                      </div>
                      <div className="absolute bg-gray-400 left-1/2 top-1/2 transition-all" style="width: 1px; height: 10px; transform: translate(-50%, -50%) translate(208.169px, 233.773px) rotate(138.316deg);">
                      </div>
                      <div className="absolute bg-gray-400 left-1/2 top-1/2 transition-all" style="width: 1px; height: 10px; transform: translate(-50%, -50%) translate(200.326px, 240.528px) rotate(140.211deg);">
                      </div>
                      <div className="absolute bg-gray-400 left-1/2 top-1/2 transition-all" style="width: 1px; height: 10px; transform: translate(-50%, -50%) translate(192.263px, 247.02px) rotate(142.105deg);">
                      </div>
                      <div className="absolute left-1/2 top-1/2 bg-[#4660EC] transition-all" style="width: 2px; height: 20px; transform: translate(-50%, -50%) translate(183.991px, 253.242px) rotate(144deg);">
                      </div>
                      <div className="absolute bg-gray-400 left-1/2 top-1/2 transition-all" style="width: 1px; height: 10px; transform: translate(-50%, -50%) translate(175.517px, 259.187px) rotate(145.895deg);">
                      </div>
                      <div className="absolute bg-gray-400 left-1/2 top-1/2 transition-all" style="width: 1px; height: 10px; transform: translate(-50%, -50%) translate(166.852px, 264.848px) rotate(147.789deg);">
                      </div>
                      <div className="absolute bg-gray-400 left-1/2 top-1/2 transition-all" style="width: 1px; height: 10px; transform: translate(-50%, -50%) translate(158.004px, 270.22px) rotate(149.684deg);">
                      </div>
                      <div className="absolute bg-gray-400 left-1/2 top-1/2 transition-all" style="width: 1px; height: 10px; transform: translate(-50%, -50%) translate(148.983px, 275.296px) rotate(151.579deg);">
                      </div>
                      <div className="absolute bg-gray-400 left-1/2 top-1/2 transition-all" style="width: 1px; height: 10px; transform: translate(-50%, -50%) translate(139.799px, 280.072px) rotate(153.474deg);">
                      </div>
                      <div className="absolute bg-gray-400 left-1/2 top-1/2 transition-all" style="width: 1px; height: 10px; transform: translate(-50%, -50%) translate(130.463px, 284.541px) rotate(155.368deg);">
                      </div>
                      <div className="absolute bg-gray-400 left-1/2 top-1/2 transition-all" style="width: 1px; height: 10px; transform: translate(-50%, -50%) translate(120.984px, 288.699px) rotate(157.263deg);">
                      </div>
                      <div className="absolute bg-gray-400 left-1/2 top-1/2 transition-all" style="width: 1px; height: 10px; transform: translate(-50%, -50%) translate(111.372px, 292.541px) rotate(159.158deg);">
                      </div>
                      <div className="absolute bg-gray-400 left-1/2 top-1/2 transition-all" style="width: 1px; height: 10px; transform: translate(-50%, -50%) translate(101.639px, 296.063px) rotate(161.053deg);">
                      </div>
                      <div className="absolute bg-gray-400 left-1/2 top-1/2 transition-all" style="width: 1px; height: 10px; transform: translate(-50%, -50%) translate(91.7943px, 299.262px) rotate(162.947deg);">
                      </div>
                      <div className="absolute bg-gray-400 left-1/2 top-1/2 transition-all" style="width: 1px; height: 10px; transform: translate(-50%, -50%) translate(81.8495px, 302.134px) rotate(164.842deg);">
                      </div>
                      <div className="absolute bg-gray-400 left-1/2 top-1/2 transition-all" style="width: 1px; height: 10px; transform: translate(-50%, -50%) translate(71.8152px, 304.675px) rotate(166.737deg);">
                      </div>
                      <div className="absolute bg-gray-400 left-1/2 top-1/2 transition-all" style="width: 1px; height: 10px; transform: translate(-50%, -50%) translate(61.7024px, 306.882px) rotate(168.632deg);">
                      </div>
                      <div className="absolute bg-gray-400 left-1/2 top-1/2 transition-all" style="width: 1px; height: 10px; transform: translate(-50%, -50%) translate(51.5221px, 308.755px) rotate(170.526deg);">
                      </div>
                      <div className="absolute bg-gray-400 left-1/2 top-1/2 transition-all" style="width: 1px; height: 10px; transform: translate(-50%, -50%) translate(41.2854px, 310.289px) rotate(172.421deg);">
                      </div>
                      <div className="absolute bg-gray-400 left-1/2 top-1/2 transition-all" style="width: 1px; height: 10px; transform: translate(-50%, -50%) translate(31.0036px, 311.485px) rotate(174.316deg);">
                      </div>
                      <div className="absolute bg-gray-400 left-1/2 top-1/2 transition-all" style="width: 1px; height: 10px; transform: translate(-50%, -50%) translate(20.6879px, 312.34px) rotate(176.211deg);">
                      </div>
                      <div className="absolute bg-gray-400 left-1/2 top-1/2 transition-all" style="width: 1px; height: 10px; transform: translate(-50%, -50%) translate(10.3496px, 312.853px) rotate(178.105deg);">
                      </div>
                      <div className="absolute bg-gray-400 left-1/2 top-1/2 transition-all" style="width: 1px; height: 10px; transform: translate(-50%, -50%) translate(1.91672e-14px, 313.024px) rotate(180deg);">
                      </div>
                      <div className="absolute bg-gray-400 left-1/2 top-1/2 transition-all" style="width: 1px; height: 10px; transform: translate(-50%, -50%) translate(-10.3496px, 312.853px) rotate(181.895deg);">
                      </div>
                      <div className="absolute bg-gray-400 left-1/2 top-1/2 transition-all" style="width: 1px; height: 10px; transform: translate(-50%, -50%) translate(-20.6879px, 312.34px) rotate(183.789deg);">
                      </div>
                      <div className="absolute bg-gray-400 left-1/2 top-1/2 transition-all" style="width: 1px; height: 10px; transform: translate(-50%, -50%) translate(-31.0036px, 311.485px) rotate(185.684deg);">
                      </div>
                      <div className="absolute bg-gray-400 left-1/2 top-1/2 transition-all" style="width: 1px; height: 10px; transform: translate(-50%, -50%) translate(-41.2854px, 310.289px) rotate(187.579deg);">
                      </div>
                      <div className="absolute bg-gray-400 left-1/2 top-1/2 transition-all" style="width: 1px; height: 10px; transform: translate(-50%, -50%) translate(-51.5221px, 308.755px) rotate(189.474deg);">
                      </div>
                      <div className="absolute bg-gray-400 left-1/2 top-1/2 transition-all" style="width: 1px; height: 10px; transform: translate(-50%, -50%) translate(-61.7024px, 306.882px) rotate(191.368deg);">
                      </div>
                      <div className="absolute bg-gray-400 left-1/2 top-1/2 transition-all" style="width: 1px; height: 10px; transform: translate(-50%, -50%) translate(-71.8152px, 304.675px) rotate(193.263deg);">
                      </div>
                      <div className="absolute bg-gray-400 left-1/2 top-1/2 transition-all" style="width: 1px; height: 10px; transform: translate(-50%, -50%) translate(-81.8495px, 302.134px) rotate(195.158deg);">
                      </div>
                      <div className="absolute bg-gray-400 left-1/2 top-1/2 transition-all" style="width: 1px; height: 10px; transform: translate(-50%, -50%) translate(-91.7943px, 299.262px) rotate(197.053deg);">
                      </div>
                      <div className="absolute bg-gray-400 left-1/2 top-1/2 transition-all" style="width: 1px; height: 10px; transform: translate(-50%, -50%) translate(-101.639px, 296.063px) rotate(198.947deg);">
                      </div>
                      <div className="absolute bg-gray-400 left-1/2 top-1/2 transition-all" style="width: 1px; height: 10px; transform: translate(-50%, -50%) translate(-111.372px, 292.541px) rotate(200.842deg);">
                      </div>
                      <div className="absolute bg-gray-400 left-1/2 top-1/2 transition-all" style="width: 1px; height: 10px; transform: translate(-50%, -50%) translate(-120.984px, 288.699px) rotate(202.737deg);">
                      </div>
                      <div className="absolute bg-gray-400 left-1/2 top-1/2 transition-all" style="width: 1px; height: 10px; transform: translate(-50%, -50%) translate(-130.463px, 284.541px) rotate(204.632deg);">
                      </div>
                      <div className="absolute bg-gray-400 left-1/2 top-1/2 transition-all" style="width: 1px; height: 10px; transform: translate(-50%, -50%) translate(-139.799px, 280.072px) rotate(206.526deg);">
                      </div>
                      <div className="absolute bg-gray-400 left-1/2 top-1/2 transition-all" style="width: 1px; height: 10px; transform: translate(-50%, -50%) translate(-148.983px, 275.296px) rotate(208.421deg);">
                      </div>
                      <div className="absolute bg-gray-400 left-1/2 top-1/2 transition-all" style="width: 1px; height: 10px; transform: translate(-50%, -50%) translate(-158.004px, 270.22px) rotate(210.316deg);">
                      </div>
                      <div className="absolute bg-gray-400 left-1/2 top-1/2 transition-all" style="width: 1px; height: 10px; transform: translate(-50%, -50%) translate(-166.852px, 264.848px) rotate(212.211deg);">
                      </div>
                      <div className="absolute bg-gray-400 left-1/2 top-1/2 transition-all" style="width: 1px; height: 10px; transform: translate(-50%, -50%) translate(-175.517px, 259.187px) rotate(214.105deg);">
                      </div>
                      <div className="absolute bg-gray-400 left-1/2 top-1/2 transition-all" style="width: 2px; height: 20px; transform: translate(-50%, -50%) translate(-183.991px, 253.242px) rotate(216deg);">
                      </div>
                      <div className="absolute bg-gray-400 left-1/2 top-1/2 transition-all" style="width: 1px; height: 10px; transform: translate(-50%, -50%) translate(-192.263px, 247.02px) rotate(217.895deg);">
                      </div>
                      <div className="absolute bg-gray-400 left-1/2 top-1/2 transition-all" style="width: 1px; height: 10px; transform: translate(-50%, -50%) translate(-200.326px, 240.528px) rotate(219.789deg);">
                      </div>
                      <div className="absolute bg-gray-400 left-1/2 top-1/2 transition-all" style="width: 1px; height: 10px; transform: translate(-50%, -50%) translate(-208.169px, 233.773px) rotate(221.684deg);">
                      </div>
                      <div className="absolute bg-gray-400 left-1/2 top-1/2 transition-all" style="width: 1px; height: 10px; transform: translate(-50%, -50%) translate(-215.784px, 226.762px) rotate(223.579deg);">
                      </div>
                      <div className="absolute bg-gray-400 left-1/2 top-1/2 transition-all" style="width: 1px; height: 10px; transform: translate(-50%, -50%) translate(-223.164px, 219.504px) rotate(225.474deg);">
                      </div>
                      <div className="absolute bg-gray-400 left-1/2 top-1/2 transition-all" style="width: 1px; height: 10px; transform: translate(-50%, -50%) translate(-230.299px, 212.005px) rotate(227.368deg);">
                      </div>
                      <div className="absolute bg-gray-400 left-1/2 top-1/2 transition-all" style="width: 1px; height: 10px; transform: translate(-50%, -50%) translate(-237.183px, 204.275px) rotate(229.263deg);">
                      </div>
                      <div className="absolute bg-gray-400 left-1/2 top-1/2 transition-all" style="width: 1px; height: 10px; transform: translate(-50%, -50%) translate(-243.807px, 196.321px) rotate(231.158deg);">
                      </div>
                      <div className="absolute bg-gray-400 left-1/2 top-1/2 transition-all" style="width: 1px; height: 10px; transform: translate(-50%, -50%) translate(-250.165px, 188.153px) rotate(233.053deg);">
                      </div>
                      <div className="absolute bg-gray-400 left-1/2 top-1/2 transition-all" style="width: 1px; height: 10px; transform: translate(-50%, -50%) translate(-256.249px, 179.779px) rotate(234.947deg);">
                      </div>
                      <div className="absolute bg-gray-400 left-1/2 top-1/2 transition-all" style="width: 1px; height: 10px; transform: translate(-50%, -50%) translate(-262.053px, 171.208px) rotate(236.842deg);">
                      </div>
                      <div className="absolute bg-gray-400 left-1/2 top-1/2 transition-all" style="width: 1px; height: 10px; transform: translate(-50%, -50%) translate(-267.571px, 162.45px) rotate(238.737deg);">
                      </div>
                      <div className="absolute bg-gray-400 left-1/2 top-1/2 transition-all" style="width: 1px; height: 10px; transform: translate(-50%, -50%) translate(-272.795px, 153.514px) rotate(240.632deg);">
                      </div>
                      <div className="absolute bg-gray-400 left-1/2 top-1/2 transition-all" style="width: 1px; height: 10px; transform: translate(-50%, -50%) translate(-277.722px, 144.411px) rotate(242.526deg);">
                      </div>
                      <div className="absolute bg-gray-400 left-1/2 top-1/2 transition-all" style="width: 1px; height: 10px; transform: translate(-50%, -50%) translate(-282.345px, 135.149px) rotate(244.421deg);">
                      </div>
                      <div className="absolute bg-gray-400 left-1/2 top-1/2 transition-all" style="width: 1px; height: 10px; transform: translate(-50%, -50%) translate(-286.659px, 125.74px) rotate(246.316deg);">
                      </div>
                      <div className="absolute bg-gray-400 left-1/2 top-1/2 transition-all" style="width: 1px; height: 10px; transform: translate(-50%, -50%) translate(-290.66px, 116.194px) rotate(248.211deg);">
                      </div>
                      <div className="absolute bg-gray-400 left-1/2 top-1/2 transition-all" style="width: 1px; height: 10px; transform: translate(-50%, -50%) translate(-294.343px, 106.52px) rotate(250.105deg);">
                      </div>
                      <div className="absolute bg-gray-400 left-1/2 top-1/2 transition-all" style="width: 1px; height: 10px; transform: translate(-50%, -50%) translate(-297.704px, 96.7297px) rotate(252deg);">
                      </div>
                      <div className="absolute bg-gray-400 left-1/2 top-1/2 transition-all" style="width: 1px; height: 10px; transform: translate(-50%, -50%) translate(-300.739px, 86.8338px) rotate(253.895deg);">
                      </div>
                      <div className="absolute bg-gray-400 left-1/2 top-1/2 transition-all" style="width: 1px; height: 10px; transform: translate(-50%, -50%) translate(-303.446px, 76.8428px) rotate(255.789deg);">
                      </div>
                      <div className="absolute bg-gray-400 left-1/2 top-1/2 transition-all" style="width: 1px; height: 10px; transform: translate(-50%, -50%) translate(-305.82px, 66.7679px) rotate(257.684deg);">
                      </div>
                      <div className="absolute bg-gray-400 left-1/2 top-1/2 transition-all" style="width: 1px; height: 10px; transform: translate(-50%, -50%) translate(-307.861px, 56.6199px) rotate(259.579deg);">
                      </div>
                      <div className="absolute bg-gray-400 left-1/2 top-1/2 transition-all" style="width: 1px; height: 10px; transform: translate(-50%, -50%) translate(-309.564px, 46.4101px) rotate(261.474deg);">
                      </div>
                      <div className="absolute bg-gray-400 left-1/2 top-1/2 transition-all" style="width: 1px; height: 10px; transform: translate(-50%, -50%) translate(-310.93px, 36.1495px) rotate(263.368deg);">
                      </div>
                      <div className="absolute bg-gray-400 left-1/2 top-1/2 transition-all" style="width: 1px; height: 10px; transform: translate(-50%, -50%) translate(-311.955px, 25.8493px) rotate(265.263deg);">
                      </div>
                      <div className="absolute bg-gray-400 left-1/2 top-1/2 transition-all" style="width: 1px; height: 10px; transform: translate(-50%, -50%) translate(-312.639px, 15.5209px) rotate(267.158deg);">
                      </div>
                      <div className="absolute bg-gray-400 left-1/2 top-1/2 transition-all" style="width: 1px; height: 10px; transform: translate(-50%, -50%) translate(-312.981px, 5.17552px) rotate(269.053deg);">
                      </div>
                      <div className="absolute bg-gray-400 left-1/2 top-1/2 transition-all" style="width: 1px; height: 10px; transform: translate(-50%, -50%) translate(-312.981px, -5.17552px) rotate(270.947deg);">
                      </div>
                      <div className="absolute bg-gray-400 left-1/2 top-1/2 transition-all" style="width: 1px; height: 10px; transform: translate(-50%, -50%) translate(-312.639px, -15.5209px) rotate(272.842deg);">
                      </div>
                      <div className="absolute bg-gray-400 left-1/2 top-1/2 transition-all" style="width: 1px; height: 10px; transform: translate(-50%, -50%) translate(-311.955px, -25.8493px) rotate(274.737deg);">
                      </div>
                      <div className="absolute bg-gray-400 left-1/2 top-1/2 transition-all" style="width: 1px; height: 10px; transform: translate(-50%, -50%) translate(-310.93px, -36.1495px) rotate(276.632deg);">
                      </div>
                      <div className="absolute bg-gray-400 left-1/2 top-1/2 transition-all" style="width: 1px; height: 10px; transform: translate(-50%, -50%) translate(-309.564px, -46.4101px) rotate(278.526deg);">
                      </div>
                      <div className="absolute bg-gray-400 left-1/2 top-1/2 transition-all" style="width: 1px; height: 10px; transform: translate(-50%, -50%) translate(-307.861px, -56.6199px) rotate(280.421deg);">
                      </div>
                      <div className="absolute bg-gray-400 left-1/2 top-1/2 transition-all" style="width: 1px; height: 10px; transform: translate(-50%, -50%) translate(-305.82px, -66.7679px) rotate(282.316deg);">
                      </div>
                      <div className="absolute bg-gray-400 left-1/2 top-1/2 transition-all" style="width: 1px; height: 10px; transform: translate(-50%, -50%) translate(-303.446px, -76.8428px) rotate(284.211deg);">
                      </div>
                      <div className="absolute bg-gray-400 left-1/2 top-1/2 transition-all" style="width: 1px; height: 10px; transform: translate(-50%, -50%) translate(-300.739px, -86.8338px) rotate(286.105deg);">
                      </div>
                      <div className="absolute bg-gray-400 left-1/2 top-1/2 transition-all" style="width: 2px; height: 20px; transform: translate(-50%, -50%) translate(-297.704px, -96.7297px) rotate(288deg);">
                      </div>
                      <div className="absolute bg-gray-400 left-1/2 top-1/2 transition-all" style="width: 1px; height: 10px; transform: translate(-50%, -50%) translate(-294.343px, -106.52px) rotate(289.895deg);">
                      </div>
                      <div className="absolute bg-gray-400 left-1/2 top-1/2 transition-all" style="width: 1px; height: 10px; transform: translate(-50%, -50%) translate(-290.66px, -116.194px) rotate(291.789deg);">
                      </div>
                      <div className="absolute bg-gray-400 left-1/2 top-1/2 transition-all" style="width: 1px; height: 10px; transform: translate(-50%, -50%) translate(-286.659px, -125.74px) rotate(293.684deg);">
                      </div>
                      <div className="absolute bg-gray-400 left-1/2 top-1/2 transition-all" style="width: 1px; height: 10px; transform: translate(-50%, -50%) translate(-282.345px, -135.149px) rotate(295.579deg);">
                      </div>
                      <div className="absolute bg-gray-400 left-1/2 top-1/2 transition-all" style="width: 1px; height: 10px; transform: translate(-50%, -50%) translate(-277.722px, -144.411px) rotate(297.474deg);">
                      </div>
                      <div className="absolute bg-gray-400 left-1/2 top-1/2 transition-all" style="width: 1px; height: 10px; transform: translate(-50%, -50%) translate(-272.795px, -153.514px) rotate(299.368deg);">
                      </div>
                      <div className="absolute bg-gray-400 left-1/2 top-1/2 transition-all" style="width: 1px; height: 10px; transform: translate(-50%, -50%) translate(-267.571px, -162.45px) rotate(301.263deg);">
                      </div>
                      <div className="absolute bg-gray-400 left-1/2 top-1/2 transition-all" style="width: 1px; height: 10px; transform: translate(-50%, -50%) translate(-262.053px, -171.208px) rotate(303.158deg);">
                      </div>
                      <div className="absolute bg-gray-400 left-1/2 top-1/2 transition-all" style="width: 1px; height: 10px; transform: translate(-50%, -50%) translate(-256.249px, -179.779px) rotate(305.053deg);">
                      </div>
                      <div className="absolute bg-gray-400 left-1/2 top-1/2 transition-all" style="width: 1px; height: 10px; transform: translate(-50%, -50%) translate(-250.165px, -188.153px) rotate(306.947deg);">
                      </div>
                      <div className="absolute bg-gray-400 left-1/2 top-1/2 transition-all" style="width: 1px; height: 10px; transform: translate(-50%, -50%) translate(-243.807px, -196.321px) rotate(308.842deg);">
                      </div>
                      <div className="absolute bg-gray-400 left-1/2 top-1/2 transition-all" style="width: 1px; height: 10px; transform: translate(-50%, -50%) translate(-237.183px, -204.275px) rotate(310.737deg);">
                      </div>
                      <div className="absolute bg-gray-400 left-1/2 top-1/2 transition-all" style="width: 1px; height: 10px; transform: translate(-50%, -50%) translate(-230.299px, -212.005px) rotate(312.632deg);">
                      </div>
                      <div className="absolute bg-gray-400 left-1/2 top-1/2 transition-all" style="width: 1px; height: 10px; transform: translate(-50%, -50%) translate(-223.164px, -219.504px) rotate(314.526deg);">
                      </div>
                      <div className="absolute bg-gray-400 left-1/2 top-1/2 transition-all" style="width: 1px; height: 10px; transform: translate(-50%, -50%) translate(-215.784px, -226.762px) rotate(316.421deg);">
                      </div>
                      <div className="absolute bg-gray-400 left-1/2 top-1/2 transition-all" style="width: 1px; height: 10px; transform: translate(-50%, -50%) translate(-208.169px, -233.773px) rotate(318.316deg);">
                      </div>
                      <div className="absolute bg-gray-400 left-1/2 top-1/2 transition-all" style="width: 1px; height: 10px; transform: translate(-50%, -50%) translate(-200.326px, -240.528px) rotate(320.211deg);">
                      </div>
                      <div className="absolute bg-gray-400 left-1/2 top-1/2 transition-all" style="width: 1px; height: 10px; transform: translate(-50%, -50%) translate(-192.263px, -247.02px) rotate(322.105deg);">
                      </div>
                      <div className="absolute bg-gray-400 left-1/2 top-1/2 transition-all" style="width: 1px; height: 10px; transform: translate(-50%, -50%) translate(-183.991px, -253.242px) rotate(324deg);">
                      </div>
                      <div className="absolute bg-gray-400 left-1/2 top-1/2 transition-all" style="width: 1px; height: 10px; transform: translate(-50%, -50%) translate(-175.517px, -259.187px) rotate(325.895deg);">
                      </div>
                      <div className="absolute bg-gray-400 left-1/2 top-1/2 transition-all" style="width: 1px; height: 10px; transform: translate(-50%, -50%) translate(-166.852px, -264.848px) rotate(327.789deg);">
                      </div>
                      <div className="absolute bg-gray-400 left-1/2 top-1/2 transition-all" style="width: 1px; height: 10px; transform: translate(-50%, -50%) translate(-158.004px, -270.22px) rotate(329.684deg);">
                      </div>
                      <div className="absolute bg-gray-400 left-1/2 top-1/2 transition-all" style="width: 1px; height: 10px; transform: translate(-50%, -50%) translate(-148.983px, -275.296px) rotate(331.579deg);">
                      </div>
                      <div className="absolute bg-gray-400 left-1/2 top-1/2 transition-all" style="width: 1px; height: 10px; transform: translate(-50%, -50%) translate(-139.799px, -280.072px) rotate(333.474deg);">
                      </div>
                      <div className="absolute bg-gray-400 left-1/2 top-1/2 transition-all" style="width: 1px; height: 10px; transform: translate(-50%, -50%) translate(-130.463px, -284.541px) rotate(335.368deg);">
                      </div>
                      <div className="absolute bg-gray-400 left-1/2 top-1/2 transition-all" style="width: 1px; height: 10px; transform: translate(-50%, -50%) translate(-120.984px, -288.699px) rotate(337.263deg);">
                      </div>
                      <div className="absolute bg-gray-400 left-1/2 top-1/2 transition-all" style="width: 1px; height: 10px; transform: translate(-50%, -50%) translate(-111.372px, -292.541px) rotate(339.158deg);">
                      </div>
                      <div className="absolute bg-gray-400 left-1/2 top-1/2 transition-all" style="width: 1px; height: 10px; transform: translate(-50%, -50%) translate(-101.639px, -296.063px) rotate(341.053deg);">
                      </div>
                      <div className="absolute bg-gray-400 left-1/2 top-1/2 transition-all" style="width: 1px; height: 10px; transform: translate(-50%, -50%) translate(-91.7943px, -299.262px) rotate(342.947deg);">
                      </div>
                      <div className="absolute bg-gray-400 left-1/2 top-1/2 transition-all" style="width: 1px; height: 10px; transform: translate(-50%, -50%) translate(-81.8495px, -302.134px) rotate(344.842deg);">
                      </div>
                      <div className="absolute bg-gray-400 left-1/2 top-1/2 transition-all" style="width: 1px; height: 10px; transform: translate(-50%, -50%) translate(-71.8152px, -304.675px) rotate(346.737deg);">
                      </div>
                      <div className="absolute bg-gray-400 left-1/2 top-1/2 transition-all" style="width: 1px; height: 10px; transform: translate(-50%, -50%) translate(-61.7024px, -306.882px) rotate(348.632deg);">
                      </div>
                      <div className="absolute bg-gray-400 left-1/2 top-1/2 transition-all" style="width: 1px; height: 10px; transform: translate(-50%, -50%) translate(-51.5221px, -308.755px) rotate(350.526deg);">
                      </div>
                      <div className="absolute bg-gray-400 left-1/2 top-1/2 transition-all" style="width: 1px; height: 10px; transform: translate(-50%, -50%) translate(-41.2854px, -310.289px) rotate(352.421deg);">
                      </div>
                      <div className="absolute bg-gray-400 left-1/2 top-1/2 transition-all" style="width: 1px; height: 10px; transform: translate(-50%, -50%) translate(-31.0036px, -311.485px) rotate(354.316deg);">
                      </div>
                      <div className="absolute bg-gray-400 left-1/2 top-1/2 transition-all" style="width: 1px; height: 10px; transform: translate(-50%, -50%) translate(-20.6879px, -312.34px) rotate(356.211deg);">
                      </div>
                      <div className="absolute bg-gray-400 left-1/2 top-1/2 transition-all" style="width: 1px; height: 10px; transform: translate(-50%, -50%) translate(-10.3496px, -312.853px) rotate(358.105deg);">
                      </div>

                      <div className="absolute transition-all duration-300" style="left: 50%; top: 50%; transform: translate(-50%, -50%) translate(1.02105e-14px, -166.75px) scale(1);">
                        <div className="flex items-center justify-center transition-all duration-300" style="transform: rotate(90deg);">
                          <div className="flex items-center gap-1.5">
                            <div className="w-8 lg:w-10 h-8 lg:h-10 rounded-full z-10 flex items-center justify-center overflow-hidden transition-all relative bg-[#4660EC1F]">
                              <div className="w-[35px] lg:w-[49px] h-[35px] lg:h-[49px] hidden -z-10 rounded-full blur-[20px] -left-1/2 -top-1/2 absolute">
                              </div>
                              <svg width="16" height="16" viewBox="0 0 16 16" fill="none" xmlns="http://www.w3.org/2000/svg" className="text-[#4660ECB2]"><g clip-path="url(#clip0_227_17991)"><path d="M5.33268 14.6707C5.70087 14.6707 5.99935 14.3722 5.99935 14.004C5.99935 13.6358 5.70087 13.3374 5.33268 13.3374C4.96449 13.3374 4.66602 13.6358 4.66602 14.004C4.66602 14.3722 4.96449 14.6707 5.33268 14.6707Z" fill="currentColor" stroke="currentColor" stroke-width="1.5" stroke-linecap="round" stroke-linejoin="round"></path><path d="M12.6667 14.6706C13.0349 14.6706 13.3333 14.3721 13.3333 14.0039C13.3333 13.6357 13.0349 13.3372 12.6667 13.3372C12.2985 13.3372 12 13.6357 12 14.0039C12 14.3721 12.2985 14.6706 12.6667 14.6706Z" fill="currentColor" stroke="currentColor" stroke-width="1.5" stroke-linecap="round" stroke-linejoin="round"></path><path d="M1.36719 1.37057H2.70052L4.47385 9.65057C4.53891 9.95381 4.70763 10.2249 4.951 10.4171C5.19436 10.6094 5.49712 10.7108 5.80719 10.7039H12.3272C12.6306 10.7034 12.9248 10.5994 13.1612 10.4091C13.3976 10.2188 13.5619 9.95359 13.6272 9.65724L14.7272 4.70391H3.41385" stroke="currentColor" stroke-width="1.5" stroke-linecap="round" stroke-linejoin="round"></path></g><defs><clipPath id="clip0_227_17991"><rect width="16" height="16" fill="currentColor" transform="translate(0 0.********)"></rect></clipPath></defs></svg>

                            </div>
                            <span className="font-normal text-xs lg:text-sm opacity-40 select-none text-black whitespace-nowrap overflow-hidden text-ellipsis max-w-[170px] text-right">Insurance</span>

                          </div>
                        </div>
                      </div>
                      <div className="absolute transition-all duration-300" style="left: 50%; top: 50%; transform: translate(-50%, -50%) translate(158.589px, -51.5286px) scale(1);">
                        <div className="flex items-center justify-center transition-all duration-300" style="transform: rotate(162deg);">
                          <div className="flex items-center gap-1.5">
                            <div className="w-8 lg:w-10 h-8 lg:h-10 rounded-full z-10 flex items-center justify-center overflow-hidden transition-all relative bg-[#4660EC1F]">
                              <div className="w-[35px] lg:w-[49px] h-[35px] lg:h-[49px] hidden -z-10 rounded-full blur-[20px] -left-1/2 -top-1/2 absolute">
                              </div>
                              <svg width="16" height="16" viewBox="0 0 16 16" fill="none" xmlns="http://www.w3.org/2000/svg" className="text-[#4660ECB2]"><g clip-path="url(#clip0_227_17991)"><path d="M5.33268 14.6707C5.70087 14.6707 5.99935 14.3722 5.99935 14.004C5.99935 13.6358 5.70087 13.3374 5.33268 13.3374C4.96449 13.3374 4.66602 13.6358 4.66602 14.004C4.66602 14.3722 4.96449 14.6707 5.33268 14.6707Z" fill="currentColor" stroke="currentColor" stroke-width="1.5" stroke-linecap="round" stroke-linejoin="round"></path><path d="M12.6667 14.6706C13.0349 14.6706 13.3333 14.3721 13.3333 14.0039C13.3333 13.6357 13.0349 13.3372 12.6667 13.3372C12.2985 13.3372 12 13.6357 12 14.0039C12 14.3721 12.2985 14.6706 12.6667 14.6706Z" fill="currentColor" stroke="currentColor" stroke-width="1.5" stroke-linecap="round" stroke-linejoin="round"></path><path d="M1.36719 1.37057H2.70052L4.47385 9.65057C4.53891 9.95381 4.70763 10.2249 4.951 10.4171C5.19436 10.6094 5.49712 10.7108 5.80719 10.7039H12.3272C12.6306 10.7034 12.9248 10.5994 13.1612 10.4091C13.3976 10.2188 13.5619 9.95359 13.6272 9.65724L14.7272 4.70391H3.41385" stroke="currentColor" stroke-width="1.5" stroke-linecap="round" stroke-linejoin="round"></path></g><defs><clipPath id="clip0_227_17991"><rect width="16" height="16" fill="currentColor" transform="translate(0 0.********)"></rect></clipPath></defs></svg>
                            </div>
                            <span className="font-normal text-xs lg:text-sm opacity-40 select-none text-black whitespace-nowrap overflow-hidden text-ellipsis max-w-[170px] text-right">Travel</span>
                          </div>
                        </div>
                      </div>
                      <div className="absolute transition-all duration-300" style="left: 50%; top: 50%; transform: translate(-50%, -50%) translate(98.0132px, 134.904px) scale(1.2);"><div className="flex items-center justify-center transition-all duration-300" style="transform: rotate(234deg);"><div className="flex items-center gap-1.5"><div className="w-8 lg:w-10 h-8 lg:h-10 rounded-full z-10 flex items-center justify-center overflow-hidden transition-all relative bg-[#4660EC] shadow-[inset_0_1px_4px_rgba(255,255,255,0.64)] [box-shadow:0_2px_12px_rgba(0,0,0,0.04)]"><div className="w-[35px] lg:w-[49px] h-[35px] lg:h-[49px] -z-10 rounded-full blur-[20px] -left-1/2 -top-1/2 absolute bg-white block">
                      </div><svg width="16" height="16" viewBox="0 0 16 16" fill="none" xmlns="http://www.w3.org/2000/svg" className="text-white"><g clip-path="url(#clip0_227_17991)"><path d="M5.33268 14.6707C5.70087 14.6707 5.99935 14.3722 5.99935 14.004C5.99935 13.6358 5.70087 13.3374 5.33268 13.3374C4.96449 13.3374 4.66602 13.6358 4.66602 14.004C4.66602 14.3722 4.96449 14.6707 5.33268 14.6707Z" fill="currentColor" stroke="currentColor" stroke-width="1.5" stroke-linecap="round" stroke-linejoin="round"></path><path d="M12.6667 14.6706C13.0349 14.6706 13.3333 14.3721 13.3333 14.0039C13.3333 13.6357 13.0349 13.3372 12.6667 13.3372C12.2985 13.3372 12 13.6357 12 14.0039C12 14.3721 12.2985 14.6706 12.6667 14.6706Z" fill="currentColor" stroke="currentColor" stroke-width="1.5" stroke-linecap="round" stroke-linejoin="round"></path><path d="M1.36719 1.37057H2.70052L4.47385 9.65057C4.53891 9.95381 4.70763 10.2249 4.951 10.4171C5.19436 10.6094 5.49712 10.7108 5.80719 10.7039H12.3272C12.6306 10.7034 12.9248 10.5994 13.1612 10.4091C13.3976 10.2188 13.5619 9.95359 13.6272 9.65724L14.7272 4.70391H3.41385" stroke="currentColor" stroke-width="1.5" stroke-linecap="round" stroke-linejoin="round"></path></g><defs><clipPath id="clip0_227_17991"><rect width="16" height="16" fill="currentColor" transform="translate(0 0.********)"></rect></clipPath></defs></svg>
                      </div><span className="text-xs lg:text-sm select-none text-black whitespace-nowrap overflow-hidden text-ellipsis max-w-[170px] text-right font-medium opacity-100">Real Estate</span>
                      </div>
                      </div>
                      </div><div className="absolute transition-all duration-300" style="left: 50%; top: 50%; transform: translate(-50%, -50%) translate(-98.0132px, 134.904px) scale(1);"><div className="flex items-center justify-center transition-all duration-300" style="transform: rotate(306deg);"><div className="flex items-center gap-1.5"><div className="w-8 lg:w-10 h-8 lg:h-10 rounded-full z-10 flex items-center justify-center overflow-hidden transition-all relative bg-[#4660EC1F]"><div className="w-[35px] lg:w-[49px] h-[35px] lg:h-[49px] hidden -z-10 rounded-full blur-[20px] -left-1/2 -top-1/2 absolute">
                      </div><svg width="16" height="16" viewBox="0 0 16 16" fill="none" xmlns="http://www.w3.org/2000/svg" className="text-[#4660ECB2]"><g clip-path="url(#clip0_227_17991)"><path d="M5.33268 14.6707C5.70087 14.6707 5.99935 14.3722 5.99935 14.004C5.99935 13.6358 5.70087 13.3374 5.33268 13.3374C4.96449 13.3374 4.66602 13.6358 4.66602 14.004C4.66602 14.3722 4.96449 14.6707 5.33268 14.6707Z" fill="currentColor" stroke="currentColor" stroke-width="1.5" stroke-linecap="round" stroke-linejoin="round"></path><path d="M12.6667 14.6706C13.0349 14.6706 13.3333 14.3721 13.3333 14.0039C13.3333 13.6357 13.0349 13.3372 12.6667 13.3372C12.2985 13.3372 12 13.6357 12 14.0039C12 14.3721 12.2985 14.6706 12.6667 14.6706Z" fill="currentColor" stroke="currentColor" stroke-width="1.5" stroke-linecap="round" stroke-linejoin="round"></path><path d="M1.36719 1.37057H2.70052L4.47385 9.65057C4.53891 9.95381 4.70763 10.2249 4.951 10.4171C5.19436 10.6094 5.49712 10.7108 5.80719 10.7039H12.3272C12.6306 10.7034 12.9248 10.5994 13.1612 10.4091C13.3976 10.2188 13.5619 9.95359 13.6272 9.65724L14.7272 4.70391H3.41385" stroke="currentColor" stroke-width="1.5" stroke-linecap="round" stroke-linejoin="round"></path></g><defs><clipPath id="clip0_227_17991"><rect width="16" height="16" fill="currentColor" transform="translate(0 0.********)"></rect></clipPath></defs></svg>
                      </div><span className="font-normal text-xs lg:text-sm opacity-40 select-none text-black whitespace-nowrap overflow-hidden text-ellipsis max-w-[170px] text-right">Retail</span>
                      </div>
                      </div>
                      </div><div className="absolute transition-all duration-300" style="left: 50%; top: 50%; transform: translate(-50%, -50%) translate(-158.589px, -51.5286px) scale(1);"><div className="flex items-center justify-center transition-all duration-300" style="transform: rotate(378deg);"><div className="flex items-center gap-1.5"><div className="w-8 lg:w-10 h-8 lg:h-10 rounded-full z-10 flex items-center justify-center overflow-hidden transition-all relative bg-[#4660EC1F]"><div className="w-[35px] lg:w-[49px] h-[35px] lg:h-[49px] hidden -z-10 rounded-full blur-[20px] -left-1/2 -top-1/2 absolute">
                      </div><svg width="16" height="16" viewBox="0 0 16 16" fill="none" xmlns="http://www.w3.org/2000/svg" className="text-[#4660ECB2]"><g clip-path="url(#clip0_227_17991)"><path d="M5.33268 14.6707C5.70087 14.6707 5.99935 14.3722 5.99935 14.004C5.99935 13.6358 5.70087 13.3374 5.33268 13.3374C4.96449 13.3374 4.66602 13.6358 4.66602 14.004C4.66602 14.3722 4.96449 14.6707 5.33268 14.6707Z" fill="currentColor" stroke="currentColor" stroke-width="1.5" stroke-linecap="round" stroke-linejoin="round"></path><path d="M12.6667 14.6706C13.0349 14.6706 13.3333 14.3721 13.3333 14.0039C13.3333 13.6357 13.0349 13.3372 12.6667 13.3372C12.2985 13.3372 12 13.6357 12 14.0039C12 14.3721 12.2985 14.6706 12.6667 14.6706Z" fill="currentColor" stroke="currentColor" stroke-width="1.5" stroke-linecap="round" stroke-linejoin="round"></path><path d="M1.36719 1.37057H2.70052L4.47385 9.65057C4.53891 9.95381 4.70763 10.2249 4.951 10.4171C5.19436 10.6094 5.49712 10.7108 5.80719 10.7039H12.3272C12.6306 10.7034 12.9248 10.5994 13.1612 10.4091C13.3976 10.2188 13.5619 9.95359 13.6272 9.65724L14.7272 4.70391H3.41385" stroke="currentColor" stroke-width="1.5" stroke-linecap="round" stroke-linejoin="round"></path></g><defs><clipPath id="clip0_227_17991"><rect width="16" height="16" fill="currentColor" transform="translate(0 0.********)"></rect></clipPath></defs></svg>
                      </div><span className="font-normal text-xs lg:text-sm opacity-40 select-none text-black whitespace-nowrap overflow-hidden text-ellipsis max-w-[170px] text-right">Banking</span>
                      </div>
                      </div>
                      </div>
                    </div>
                    <div className="absolute z-30 right-0 top-0 h-full w-[540px] pointer-events-none" style="mask-image: linear-gradient(270deg, rgb(255, 255, 255) 0%, rgb(255, 255, 255) 60%, rgba(255, 255, 255, 0) 100%); mask-size: 100% 100%; mask-repeat: no-repeat; background: rgb(255, 255, 255);">
                    </div>
                  </div>
                </div>
              </div>
            </div>
          </div>


        </div>


      </div>




    </div>
  );
}
